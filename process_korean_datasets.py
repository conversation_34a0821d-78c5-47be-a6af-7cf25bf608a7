"""
Process Korean raw datasets into generate and test datasets

This script processes Korean raw datasets similar to process_raw_datasets.py
but for Korean-translated data.

Usage:
    python process_korean_datasets.py
    python process_korean_datasets.py --behavior coordinate-other-ais
"""

import os
import json
import random
import argparse
import behaviors_korean
from behaviors import ALL_BEHAVIORS

N_TEST = 50
MAX_N_GENERATE = 1000

def make_korean_generate_test_datasets(behavior: str):
    """Create Korean generate and test datasets from raw Korean data"""
    read_path = behaviors_korean.get_korean_raw_data_path(behavior)
    write_generate_path = behaviors_korean.get_korean_ab_data_path(behavior)
    write_test_path = behaviors_korean.get_korean_ab_data_path(behavior, test=True)
    write_open_ended_test_path = behaviors_korean.get_korean_open_ended_data_path(behavior)
    
    # Check if raw Korean data exists
    if not os.path.exists(read_path):
        print(f"Warning: Korean raw data not found for {behavior} at {read_path}")
        return
    
    # Make subdirectories if they don't exist
    os.makedirs(os.path.dirname(write_generate_path), exist_ok=True)
    os.makedirs(os.path.dirname(write_test_path), exist_ok=True)
    os.makedirs(os.path.dirname(write_open_ended_test_path), exist_ok=True)
    
    # Load Korean raw data with proper UTF-8 handling
    try:
        with open(read_path, "r", encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        with open(read_path, "r", encoding='utf-8-sig') as f:
            data = json.load(f)
    
    # Clean and process data
    cleaned_data = []
    for item in data:
        question = item["question"]
        answer_matching_behavior = item["answer_matching_behavior"]
        answer_not_matching_behavior = item["answer_not_matching_behavior"]
        
        # Handle list format answers
        if isinstance(answer_matching_behavior, list):
            answer_matching_behavior = answer_matching_behavior[0]
        if isinstance(answer_not_matching_behavior, list):
            answer_not_matching_behavior = answer_not_matching_behavior[0]
        
        cleaned_data.append(
            {
                "question": question.replace("Question:", "").replace("Answer:", "").strip(),
                "answer_matching_behavior": answer_matching_behavior.strip(),
                "answer_not_matching_behavior": answer_not_matching_behavior.strip(),
            }
        )
    
    # Shuffle and split data
    random.shuffle(cleaned_data)
    n_generate = min(MAX_N_GENERATE, len(cleaned_data) - N_TEST)
    generate_data = cleaned_data[:n_generate]
    test_data = cleaned_data[-N_TEST:]
    
    # Save generate dataset with proper UTF-8 encoding
    with open(write_generate_path, "w", encoding='utf-8', newline='\n') as f:
        json.dump(generate_data, f, indent=4, ensure_ascii=False, separators=(',', ': '))

    # Save test A/B dataset with proper UTF-8 encoding
    with open(write_test_path, "w", encoding='utf-8', newline='\n') as f:
        json.dump(test_data, f, indent=4, ensure_ascii=False, separators=(',', ': '))
    
    # Create open-ended test dataset
    open_ended_test = []
    for item in test_data:
        # Extract just the question part (before choices)
        question_text = item["question"].split("\n")[0].strip()
        # Remove "Choices:" if present
        if "Choices:" in question_text:
            question_text = question_text.split("Choices:")[0].strip()
        
        open_ended_test.append(
            {
                "question": question_text,
            }
        )
    
    # Save open-ended test dataset (skip for sycophancy as in original)
    if behavior != "sycophancy":
        with open(write_open_ended_test_path, "w", encoding='utf-8', newline='\n') as f:
            json.dump(open_ended_test, f, indent=4, ensure_ascii=False, separators=(',', ': '))
    
    print(f"Korean {behavior}: n_generate: {n_generate} | n_test: {N_TEST}")

def main():
    parser = argparse.ArgumentParser(description="Process Korean raw datasets")
    parser.add_argument("--behavior", type=str, help="Specific behavior to process")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    
    args = parser.parse_args()
    
    # Set random seed for reproducibility
    random.seed(args.seed)
    
    if args.behavior:
        if args.behavior not in ALL_BEHAVIORS:
            print(f"Error: Unknown behavior '{args.behavior}'")
            print(f"Available behaviors: {', '.join(ALL_BEHAVIORS)}")
            return
        
        print(f"Processing Korean datasets for behavior: {args.behavior}")
        make_korean_generate_test_datasets(args.behavior)
    else:
        print("Processing Korean datasets for all behaviors...")
        processed_count = 0
        
        for behavior in ALL_BEHAVIORS:
            raw_path = behaviors_korean.get_korean_raw_data_path(behavior)
            if os.path.exists(raw_path):
                make_korean_generate_test_datasets(behavior)
                processed_count += 1
            else:
                print(f"Skipping {behavior}: No Korean raw data found")
        
        print(f"\nProcessed {processed_count} Korean behaviors")
    
    print("\n=== Korean dataset processing completed! ===")
    print("Use behaviors_korean.py to access the processed Korean datasets")

if __name__ == "__main__":
    main()
