# Korean Dataset Translation System

This system provides tools to create and work with Korean-translated versions of the CAA (Contrastive Activation Addition) datasets.

## Overview

The Korean dataset system includes:
- **Translation tools**: Translate English datasets to Korean using OpenAI GPT-4
- **Processing tools**: Process Korean raw datasets into generate/test splits
- **Analysis tools**: Generate steering vectors and analyze Korean datasets
- **Utilities**: Helper functions for accessing Korean datasets

## Files Created

1. **`translate_datasets_korean.py`** - Main translation script
2. **`behaviors_korean.py`** - Korean dataset path utilities
3. **`process_korean_datasets.py`** - Process Korean raw datasets
4. **`generate_vectors_korean.py`** - Generate steering vectors for Korean data
5. **`README_Korean_Datasets.md`** - This documentation

## Quick Start

### 1. Prerequisites

Ensure you have:
- OpenAI API key with GPT-4 access (set in `.env` as `OPEN_AI_KEY`)
- All original requirements installed (`pip install -r requirements.txt`)

### 2. Translate Datasets

Translate all behaviors:
```bash
python translate_datasets_korean.py --all
```

Translate specific behavior:
```bash
python translate_datasets_korean.py --behavior coordinate-other-ais
```

### 3. Process Korean Datasets

After translation, process the raw Korean data:
```bash
python process_korean_datasets.py
```

### 4. Generate Korean Steering Vectors

```bash
python generate_vectors_korean.py --layers $(seq 0 31) --save_activations --model_size 7b
```

### 5. Check Available Korean Datasets

```bash
python behaviors_korean.py
```

## Detailed Usage

### Translation (`translate_datasets_korean.py`)

The translation script uses OpenAI GPT-4 to translate datasets while preserving:
- Multiple choice format (A), (B), etc.
- Original structure and formatting
- JSON schema

**Options:**
- `--behavior <name>`: Translate specific behavior
- `--all`: Translate all behaviors
- `--api_key <key>`: Override API key from .env

**Example:**
```bash
# Translate refusal behavior
python translate_datasets_korean.py --behavior refusal

# Translate all with custom API key
python translate_datasets_korean.py --all --api_key sk-...
```

### Processing (`process_korean_datasets.py`)

Processes Korean raw datasets into generate/test splits, similar to the original `process_raw_datasets.py`.

**Features:**
- Creates generate datasets (for vector generation)
- Creates test A/B datasets (for evaluation)
- Creates open-ended test datasets
- Maintains same split ratios as original (50 test, up to 1000 generate)

**Options:**
- `--behavior <name>`: Process specific behavior
- `--seed <int>`: Random seed for reproducibility (default: 42)

### Vector Generation (`generate_vectors_korean.py`)

Generates steering vectors using Korean datasets.

**Options:**
- `--layers <list>`: Layers to process (e.g., `$(seq 0 31)`)
- `--behaviors <list>`: Specific behaviors (default: all available)
- `--model_size <7b|13b>`: Model size
- `--use_base_model`: Use base model instead of chat
- `--save_activations`: Save raw activations

**Example:**
```bash
# Generate vectors for all layers, 7B chat model
python generate_vectors_korean.py --layers $(seq 0 31) --save_activations --model_size 7b

# Generate for specific behaviors and base model
python generate_vectors_korean.py --layers 10 15 20 --behaviors refusal sycophancy --use_base_model
```

### Korean Dataset Utilities (`behaviors_korean.py`)

Provides functions to access Korean datasets:

```python
import behaviors_korean

# Get Korean dataset paths
raw_path = behaviors_korean.get_korean_raw_data_path("refusal")
test_path = behaviors_korean.get_korean_ab_data_path("refusal", test=True)

# Load Korean data
test_data = behaviors_korean.get_korean_ab_test_data("refusal")

# Check what's available
available = behaviors_korean.list_available_korean_behaviors()
stats = behaviors_korean.get_korean_dataset_stats("refusal")
```

## Directory Structure

Korean datasets are organized as:
```
datasets/korean/
├── raw/
│   ├── coordinate-other-ais/
│   │   └── dataset.json
│   ├── refusal/
│   │   └── dataset.json
│   └── ...
├── generate/
│   ├── coordinate-other-ais/
│   │   └── generate_dataset.json
│   └── ...
└── test/
    ├── coordinate-other-ais/
    │   ├── test_dataset_ab.json
    │   └── test_dataset_open_ended.json
    └── ...
```

Korean vectors and results:
```
vectors/korean/
normalized_vectors/korean/
activations/korean/
results/korean/
analysis/korean/
```

## Integration with Existing Code

The Korean system is designed to work alongside the original English system:

1. **Parallel structure**: Korean datasets mirror the original structure
2. **Compatible APIs**: Korean utilities follow the same patterns as `behaviors.py`
3. **Separate storage**: Korean data doesn't interfere with original data
4. **Reusable components**: Uses existing tokenization, model wrappers, etc.

## Translation Quality

The translation system:
- Uses GPT-4 for high-quality translation
- Preserves multiple choice formats
- Maintains technical terminology consistency
- Includes rate limiting to avoid API issues
- Handles errors gracefully (falls back to original text)

## Cost Estimation

Translation costs depend on dataset size and OpenAI pricing:
- ~2000 items per behavior × 7 behaviors = ~14,000 items
- Average ~100 tokens per item = ~1.4M tokens
- At current GPT-4 rates: ~$30-50 for full translation

## Troubleshooting

**Translation fails:**
- Check OpenAI API key in `.env`
- Verify API quota/billing
- Check internet connection

**Korean datasets not found:**
- Run translation first: `python translate_datasets_korean.py --all`
- Check file permissions
- Verify directory structure

**Vector generation fails:**
- Ensure Korean datasets are processed: `python process_korean_datasets.py`
- Check HuggingFace token for model access
- Verify GPU memory for large models

## Next Steps

After creating Korean datasets, you can:
1. Compare English vs Korean steering vectors
2. Analyze cross-lingual behavior consistency
3. Test model performance on Korean prompts
4. Extend to other languages using the same framework

## Contributing

To extend this system:
1. Add new behaviors to translation pipeline
2. Implement additional translation services (Azure, Google, etc.)
3. Add Korean-specific evaluation metrics
4. Create cross-lingual analysis tools
