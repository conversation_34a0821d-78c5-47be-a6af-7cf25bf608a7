"""
Korean dataset behaviors module

This module provides functions to access Korean-translated datasets,
similar to the original behaviors.py but for Korean versions.
"""

import os
import json
from typing import Optional, Literal
from utils.helpers import make_tensor_save_suffix

# Korean dataset paths
KOREAN_BASE_PATH = "datasets/korean"
KOREAN_RAW_DATA_PATH = os.path.join(KOREAN_BASE_PATH, "raw")
KOREAN_GENERATE_DATA_PATH = os.path.join(KOREAN_BASE_PATH, "generate")
KOREAN_TEST_DATA_PATH = os.path.join(KOREAN_BASE_PATH, "test")
KOREAN_VECTORS_PATH = "vectors/korean"
KOREAN_NORMALIZED_VECTORS_PATH = "normalized_vectors/korean"
KOREAN_ACTIVATIONS_PATH = "activations/korean"
KOREAN_RESULTS_PATH = "results/korean"
KOREAN_ANALYSIS_PATH = "analysis/korean"

# Import behavior names from original module
from behaviors import ALL_BEHAVIORS, HUMAN_NAMES

def get_korean_raw_data_path(behavior: str) -> str:
    """Get path to Korean raw dataset"""
    return os.path.join(KOREAN_RAW_DATA_PATH, behavior, "dataset.json")

def get_korean_ab_data_path(behavior: str, test: bool = False) -> str:
    """Get path to Korean A/B dataset"""
    if test:
        path = os.path.join(KOREAN_TEST_DATA_PATH, behavior, "test_dataset_ab.json")
    else:
        path = os.path.join(KOREAN_GENERATE_DATA_PATH, behavior, "generate_dataset.json")
    return path

def get_korean_open_ended_data_path(behavior: str) -> str:
    """Get path to Korean open-ended test dataset"""
    return os.path.join(KOREAN_TEST_DATA_PATH, behavior, "test_dataset_open_ended.json")

def get_korean_vector_dir(behavior: str, normalized: bool = False) -> str:
    """Get Korean vector directory"""
    if normalized:
        return os.path.join(KOREAN_NORMALIZED_VECTORS_PATH, behavior)
    else:
        return os.path.join(KOREAN_VECTORS_PATH, behavior)

def get_korean_vector_path(behavior: str, layer, model_name_path: str, normalized=False) -> str:
    """Get Korean vector file path"""
    return os.path.join(
        get_korean_vector_dir(behavior, normalized=normalized),
        f"vec_layer_{make_tensor_save_suffix(layer, model_name_path)}.pt",
    )

def get_korean_activations_dir(behavior: str) -> str:
    """Get Korean activations directory"""
    return os.path.join(KOREAN_ACTIVATIONS_PATH, behavior)

def get_korean_activations_path(behavior: str, layer, model_name_path: str) -> str:
    """Get Korean activations file path"""
    return os.path.join(
        get_korean_activations_dir(behavior),
        f"activations_layer_{make_tensor_save_suffix(layer, model_name_path)}.pt",
    )

def get_korean_results_dir(behavior: str) -> str:
    """Get Korean results directory"""
    return os.path.join(KOREAN_RESULTS_PATH, behavior)

def get_korean_analysis_dir() -> str:
    """Get Korean analysis directory"""
    return KOREAN_ANALYSIS_PATH

def get_korean_ab_test_data(behavior: str):
    """Load Korean A/B test data"""
    try:
        with open(get_korean_ab_data_path(behavior, test=True), "r", encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        with open(get_korean_ab_data_path(behavior, test=True), "r", encoding='utf-8-sig') as f:
            data = json.load(f)
    return data

def get_korean_open_ended_test_data(behavior: str):
    """Load Korean open-ended test data"""
    try:
        with open(get_korean_open_ended_data_path(behavior), "r", encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        with open(get_korean_open_ended_data_path(behavior), "r", encoding='utf-8-sig') as f:
            data = json.load(f)
    return data

def get_korean_raw_data(behavior: str):
    """Load Korean raw data"""
    try:
        with open(get_korean_raw_data_path(behavior), "r", encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        with open(get_korean_raw_data_path(behavior), "r", encoding='utf-8-sig') as f:
            data = json.load(f)
    return data

def get_korean_generate_data(behavior: str):
    """Load Korean generate data"""
    try:
        with open(get_korean_ab_data_path(behavior, test=False), "r", encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        with open(get_korean_ab_data_path(behavior, test=False), "r", encoding='utf-8-sig') as f:
            data = json.load(f)
    return data

def check_korean_datasets_exist(behavior: str) -> dict:
    """Check which Korean datasets exist for a behavior"""
    datasets = {
        'raw': os.path.exists(get_korean_raw_data_path(behavior)),
        'generate': os.path.exists(get_korean_ab_data_path(behavior, test=False)),
        'test_ab': os.path.exists(get_korean_ab_data_path(behavior, test=True)),
        'test_open_ended': os.path.exists(get_korean_open_ended_data_path(behavior))
    }
    return datasets

def list_available_korean_behaviors() -> list:
    """List behaviors that have Korean datasets available"""
    available = []
    for behavior in ALL_BEHAVIORS:
        datasets = check_korean_datasets_exist(behavior)
        if any(datasets.values()):
            available.append(behavior)
    return available

def get_korean_dataset_stats(behavior: str) -> dict:
    """Get statistics about Korean datasets for a behavior"""
    stats = {}
    
    try:
        if os.path.exists(get_korean_raw_data_path(behavior)):
            raw_data = get_korean_raw_data(behavior)
            stats['raw_count'] = len(raw_data)
    except:
        stats['raw_count'] = 0
    
    try:
        if os.path.exists(get_korean_ab_data_path(behavior, test=False)):
            generate_data = get_korean_generate_data(behavior)
            stats['generate_count'] = len(generate_data)
    except:
        stats['generate_count'] = 0
    
    try:
        if os.path.exists(get_korean_ab_data_path(behavior, test=True)):
            test_data = get_korean_ab_test_data(behavior)
            stats['test_ab_count'] = len(test_data)
    except:
        stats['test_ab_count'] = 0
    
    try:
        if os.path.exists(get_korean_open_ended_data_path(behavior)):
            open_ended_data = get_korean_open_ended_test_data(behavior)
            stats['test_open_ended_count'] = len(open_ended_data)
    except:
        stats['test_open_ended_count'] = 0
    
    return stats

def print_korean_dataset_summary():
    """Print a summary of available Korean datasets"""
    print("=== Korean Dataset Summary ===")
    available_behaviors = list_available_korean_behaviors()
    
    if not available_behaviors:
        print("No Korean datasets found. Run translate_datasets_korean.py to create them.")
        return
    
    print(f"Available behaviors: {len(available_behaviors)}")
    
    for behavior in available_behaviors:
        stats = get_korean_dataset_stats(behavior)
        datasets = check_korean_datasets_exist(behavior)
        
        print(f"\n{behavior}:")
        print(f"  Raw: {'✓' if datasets['raw'] else '✗'} ({stats.get('raw_count', 0)} items)")
        print(f"  Generate: {'✓' if datasets['generate'] else '✗'} ({stats.get('generate_count', 0)} items)")
        print(f"  Test A/B: {'✓' if datasets['test_ab'] else '✗'} ({stats.get('test_ab_count', 0)} items)")
        print(f"  Test Open-ended: {'✓' if datasets['test_open_ended'] else '✗'} ({stats.get('test_open_ended_count', 0)} items)")

if __name__ == "__main__":
    print_korean_dataset_summary()
