"""
Generate steering vectors for Korean datasets

This script generates steering vectors using Korean-translated datasets,
similar to generate_vectors.py but for Korean data.

Example usage:
python generate_vectors_korean.py --layers $(seq 0 31) --save_activations --model_size 7b --behaviors coordinate-other-ais
python generate_vectors_korean.py --layers $(seq 0 31) --model_size 7b --use_base_model
"""

import json
import torch as t
from torch.utils.data import Dataset
from transformers import AutoTokenizer
from tqdm import tqdm
import os
from dotenv import load_dotenv
from llama_wrapper import LlamaWrapper
import argparse
from typing import List
from utils.tokenize import tokenize_llama_base, tokenize_llama_chat
import behaviors_korean
from behaviors import ALL_BEHAVIORS
from utils.helpers import get_model_path, make_tensor_save_suffix

load_dotenv()

HUGGINGFACE_TOKEN = os.getenv("HF_TOKEN")


class KoreanComparisonDataset(Dataset):
    def __init__(self, data_path, token, model_name_path, use_chat):
        # Load Korean data with proper UTF-8 handling
        try:
            with open(data_path, "r", encoding='utf-8') as f:
                self.data = json.load(f)
        except UnicodeDecodeError:
            with open(data_path, "r", encoding='utf-8-sig') as f:
                self.data = json.load(f)
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name_path, token=token
        )
        self.tokenizer.pad_token = self.tokenizer.eos_token
        self.use_chat = use_chat

    def prompt_to_tokens(self, instruction, model_output):
        if self.use_chat:
            tokens = tokenize_llama_chat(
                self.tokenizer,
                user_input=instruction,
                model_output=model_output,
            )
        else:
            tokens = tokenize_llama_base(
                self.tokenizer,
                user_input=instruction,
                model_output=model_output,
            )
        return t.tensor(tokens).unsqueeze(0)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        p_text = item["answer_matching_behavior"]
        n_text = item["answer_not_matching_behavior"]
        q_text = item["question"]
        p_tokens = self.prompt_to_tokens(q_text, p_text)
        n_tokens = self.prompt_to_tokens(q_text, n_text)
        return p_tokens, n_tokens


def get_korean_activations(
    model: LlamaWrapper,
    dataset: KoreanComparisonDataset,
    layers: List[int],
) -> t.Tensor:
    """Get activations for Korean dataset"""
    device = model.device
    n_layers = len(layers)
    d_model = model.model.config.hidden_size

    pos_activations = t.zeros(n_layers, len(dataset), d_model).to(device)
    neg_activations = t.zeros(n_layers, len(dataset), d_model).to(device)

    for i, (p_tokens, n_tokens) in enumerate(tqdm(dataset, desc="Processing Korean data")):
        p_tokens, n_tokens = p_tokens.to(device), n_tokens.to(device)

        # Get positive activations
        model.reset_all()
        model.get_logits(p_tokens)
        for j, layer in enumerate(layers):
            pos_activations[j, i] = model.get_last_activations(layer).detach().cpu()

        # Get negative activations
        model.reset_all()
        model.get_logits(n_tokens)
        for j, layer in enumerate(layers):
            neg_activations[j, i] = model.get_last_activations(layer).detach().cpu()

    return pos_activations, neg_activations


def generate_korean_vectors(
    layers: List[int],
    model_name_path: str,
    behaviors: List[str],
    save_activations: bool = False,
    use_base_model: bool = False,
):
    """Generate steering vectors for Korean datasets"""
    print(f"Generating Korean vectors for behaviors: {behaviors}")
    print(f"Model: {model_name_path}")
    print(f"Layers: {layers}")
    print(f"Use base model: {use_base_model}")

    model = LlamaWrapper(model_name_path, HUGGINGFACE_TOKEN, use_chat=not use_base_model)

    for behavior in behaviors:
        print(f"\n=== Processing Korean behavior: {behavior} ===")
        
        # Check if Korean dataset exists
        data_path = behaviors_korean.get_korean_ab_data_path(behavior, test=False)
        if not os.path.exists(data_path):
            print(f"Warning: Korean dataset not found for {behavior} at {data_path}")
            print("Run translate_datasets_korean.py and process_korean_datasets.py first")
            continue

        dataset = KoreanComparisonDataset(
            data_path, HUGGINGFACE_TOKEN, model_name_path, use_chat=not use_base_model
        )

        print(f"Loaded {len(dataset)} Korean examples for {behavior}")

        pos_activations, neg_activations = get_korean_activations(model, dataset, layers)

        # Create directories
        vector_dir = behaviors_korean.get_korean_vector_dir(behavior)
        os.makedirs(vector_dir, exist_ok=True)

        if save_activations:
            activations_dir = behaviors_korean.get_korean_activations_dir(behavior)
            os.makedirs(activations_dir, exist_ok=True)

        # Save vectors and activations
        for i, layer in enumerate(layers):
            # Calculate steering vector (difference of means)
            pos_mean_act = pos_activations[i].mean(dim=0)
            neg_mean_act = neg_activations[i].mean(dim=0)
            vector = pos_mean_act - neg_mean_act

            # Save vector
            vector_path = behaviors_korean.get_korean_vector_path(behavior, layer, model_name_path)
            t.save(vector, vector_path)

            # Save activations if requested
            if save_activations:
                activations_path = behaviors_korean.get_korean_activations_path(behavior, layer, model_name_path)
                t.save(
                    {
                        "pos_activations": pos_activations[i],
                        "neg_activations": neg_activations[i],
                    },
                    activations_path,
                )

        print(f"Saved Korean vectors for {behavior} (layers {min(layers)}-{max(layers)})")


def main():
    parser = argparse.ArgumentParser(description="Generate Korean steering vectors")
    parser.add_argument("--layers", nargs="+", type=int, required=True, help="Layers to generate vectors for")
    parser.add_argument("--behaviors", nargs="+", type=str, help="Behaviors to process (default: all available)")
    parser.add_argument("--model_size", type=str, default="7b", choices=["7b", "13b"], help="Model size")
    parser.add_argument("--use_base_model", action="store_true", help="Use base model instead of chat model")
    parser.add_argument("--save_activations", action="store_true", help="Save raw activations")

    args = parser.parse_args()

    # Get model path
    model_name_path = get_model_path(args.model_size, args.use_base_model)

    # Determine behaviors to process
    if args.behaviors:
        behaviors_to_process = args.behaviors
        # Validate behaviors
        for behavior in behaviors_to_process:
            if behavior not in ALL_BEHAVIORS:
                print(f"Error: Unknown behavior '{behavior}'")
                print(f"Available behaviors: {', '.join(ALL_BEHAVIORS)}")
                return
    else:
        # Use all available Korean behaviors
        behaviors_to_process = behaviors_korean.list_available_korean_behaviors()
        if not behaviors_to_process:
            print("No Korean datasets found. Run translate_datasets_korean.py first.")
            return

    print(f"Processing Korean behaviors: {behaviors_to_process}")

    generate_korean_vectors(
        layers=args.layers,
        model_name_path=model_name_path,
        behaviors=behaviors_to_process,
        save_activations=args.save_activations,
        use_base_model=args.use_base_model,
    )

    print("\n=== Korean vector generation completed! ===")


if __name__ == "__main__":
    main()
