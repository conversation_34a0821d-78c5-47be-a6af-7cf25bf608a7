#!/usr/bin/env python3
"""
Complete Korean Dataset Pipeline

This script provides a convenient way to run the entire Korean dataset
creation and processing pipeline.

Usage:
    python korean_pipeline.py --full-pipeline
    python korean_pipeline.py --translate-only
    python korean_pipeline.py --process-only
    python korean_pipeline.py --vectors-only
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import behaviors_korean
from behaviors import ALL_BEHAVIORS

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ SUCCESS")
        if result.stdout:
            print("STDOUT:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("✗ FAILED")
        print("STDERR:", e.stderr)
        if e.stdout:
            print("STDOUT:", e.stdout)
        return False

def check_prerequisites():
    """Check if prerequisites are met"""
    print("Checking prerequisites...")
    
    # Check if .env file exists and has required keys
    env_path = Path(".env")
    if not env_path.exists():
        print("✗ .env file not found")
        return False
    
    # Check for OpenAI API key
    with open(env_path) as f:
        env_content = f.read()
        if "OPEN_AI_KEY" not in env_content:
            print("✗ OPEN_AI_KEY not found in .env file")
            return False
        if "HF_TOKEN" not in env_content:
            print("✗ HF_TOKEN not found in .env file")
            return False
    
    # Check if original datasets exist
    missing_behaviors = []
    for behavior in ALL_BEHAVIORS:
        raw_path = f"datasets/raw/{behavior}/dataset.json"
        if not os.path.exists(raw_path):
            missing_behaviors.append(behavior)
    
    if missing_behaviors:
        print(f"✗ Missing original datasets for: {', '.join(missing_behaviors)}")
        return False
    
    print("✓ Prerequisites check passed")
    return True

def translate_datasets(behaviors=None):
    """Translate datasets to Korean"""
    if behaviors:
        for behavior in behaviors:
            command = f"python translate_datasets_korean.py --behavior {behavior}"
            success = run_command(command, f"Translating {behavior} dataset")
            if not success:
                return False
    else:
        command = "python translate_datasets_korean.py --all"
        success = run_command(command, "Translating all datasets")
        if not success:
            return False
    
    return True

def process_datasets(behaviors=None):
    """Process Korean raw datasets"""
    if behaviors:
        for behavior in behaviors:
            command = f"python process_korean_datasets.py --behavior {behavior}"
            success = run_command(command, f"Processing {behavior} Korean dataset")
            if not success:
                return False
    else:
        command = "python process_korean_datasets.py"
        success = run_command(command, "Processing all Korean datasets")
        if not success:
            return False
    
    return True

def generate_vectors(layers="0 15 31", model_size="7b", behaviors=None):
    """Generate Korean steering vectors"""
    layers_seq = " ".join(map(str, range(int(layers.split()[0]), int(layers.split()[-1]) + 1)))
    
    if behaviors:
        behavior_arg = f"--behaviors {' '.join(behaviors)}"
    else:
        behavior_arg = ""
    
    command = f"python generate_vectors_korean.py --layers {layers_seq} --save_activations --model_size {model_size} {behavior_arg}"
    success = run_command(command, f"Generating Korean vectors (layers {layers}, {model_size})")
    return success

def show_status():
    """Show current status of Korean datasets"""
    print("\n" + "="*60)
    print("KOREAN DATASET STATUS")
    print("="*60)
    
    behaviors_korean.print_korean_dataset_summary()
    
    # Check for vectors
    print("\nKorean Vectors:")
    vector_base = Path("vectors/korean")
    if vector_base.exists():
        vector_behaviors = [d.name for d in vector_base.iterdir() if d.is_dir()]
        print(f"  Available: {', '.join(vector_behaviors) if vector_behaviors else 'None'}")
    else:
        print("  Available: None")

def main():
    parser = argparse.ArgumentParser(description="Korean Dataset Pipeline")
    parser.add_argument("--full-pipeline", action="store_true", 
                       help="Run complete pipeline: translate -> process -> vectors")
    parser.add_argument("--translate-only", action="store_true", 
                       help="Only translate datasets")
    parser.add_argument("--process-only", action="store_true", 
                       help="Only process Korean raw datasets")
    parser.add_argument("--vectors-only", action="store_true", 
                       help="Only generate vectors")
    parser.add_argument("--status", action="store_true", 
                       help="Show current status")
    parser.add_argument("--behaviors", nargs="+", 
                       help="Specific behaviors to process")
    parser.add_argument("--layers", default="0 31", 
                       help="Layer range for vector generation (default: 0 31)")
    parser.add_argument("--model-size", default="7b", choices=["7b", "13b"],
                       help="Model size for vector generation")
    parser.add_argument("--skip-checks", action="store_true",
                       help="Skip prerequisite checks")
    
    args = parser.parse_args()
    
    if not any([args.full_pipeline, args.translate_only, args.process_only, 
                args.vectors_only, args.status]):
        parser.print_help()
        return
    
    # Show status
    if args.status:
        show_status()
        return
    
    # Check prerequisites
    if not args.skip_checks and not check_prerequisites():
        print("\n✗ Prerequisites not met. Please fix the issues above.")
        return
    
    print(f"\nStarting Korean dataset pipeline...")
    print(f"Behaviors: {args.behaviors if args.behaviors else 'all'}")
    
    success = True
    
    # Run translation
    if args.full_pipeline or args.translate_only:
        print("\n🔄 Starting translation phase...")
        success = translate_datasets(args.behaviors)
        if not success:
            print("✗ Translation failed. Stopping pipeline.")
            return
    
    # Run processing
    if success and (args.full_pipeline or args.process_only):
        print("\n🔄 Starting processing phase...")
        success = process_datasets(args.behaviors)
        if not success:
            print("✗ Processing failed. Stopping pipeline.")
            return
    
    # Run vector generation
    if success and (args.full_pipeline or args.vectors_only):
        print("\n🔄 Starting vector generation phase...")
        success = generate_vectors(args.layers, args.model_size, args.behaviors)
        if not success:
            print("✗ Vector generation failed. Stopping pipeline.")
            return
    
    if success:
        print("\n" + "="*60)
        print("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*60)
        show_status()
        
        print("\nNext steps:")
        print("1. Use behaviors_korean.py to access Korean datasets")
        print("2. Run analysis on Korean vs English vectors")
        print("3. Test model performance on Korean prompts")
        print("4. Compare cross-lingual behavior consistency")
    else:
        print("\n✗ Pipeline failed. Check error messages above.")

if __name__ == "__main__":
    main()
