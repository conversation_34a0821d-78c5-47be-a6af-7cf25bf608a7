#!/usr/bin/env python3
"""
Validate Korean Dataset Encoding

This script validates that all Korean datasets are properly encoded
and can be read/written without UTF-8 issues.

Usage:
    python validate_korean_encoding.py
    python validate_korean_encoding.py --behavior coordinate-other-ais
"""

import os
import json
import argparse
import unicodedata
from pathlib import Path
import behaviors_korean
from behaviors import ALL_BEHAVIORS

def validate_file_encoding(file_path: str) -> dict:
    """Validate encoding of a single file"""
    result = {
        'path': file_path,
        'exists': False,
        'readable_utf8': False,
        'contains_korean': False,
        'unicode_normalized': False,
        'json_valid': False,
        'error': None
    }
    
    if not os.path.exists(file_path):
        result['error'] = 'File does not exist'
        return result
    
    result['exists'] = True
    
    try:
        # Test UTF-8 reading
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        result['readable_utf8'] = True
        
        # Test JSON parsing
        data = json.loads(content)
        result['json_valid'] = True
        
        # Check for Korean characters
        has_korean = any(
            any(ord(c) >= 0xAC00 and ord(c) <= 0xD7AF for c in str(value))  # Hangul syllables
            for item in data if isinstance(item, dict)
            for value in item.values() if isinstance(value, str)
        )
        result['contains_korean'] = has_korean
        
        # Check Unicode normalization
        normalized_content = unicodedata.normalize('NFKC', content)
        result['unicode_normalized'] = (content == normalized_content)
        
    except UnicodeDecodeError as e:
        result['error'] = f'UTF-8 decode error: {e}'
    except json.JSONDecodeError as e:
        result['error'] = f'JSON parse error: {e}'
    except Exception as e:
        result['error'] = f'Unexpected error: {e}'
    
    return result

def validate_behavior_datasets(behavior: str) -> dict:
    """Validate all datasets for a behavior"""
    print(f"\n🔍 Validating {behavior} datasets...")
    
    datasets = {
        'raw': behaviors_korean.get_korean_raw_data_path(behavior),
        'generate': behaviors_korean.get_korean_ab_data_path(behavior, test=False),
        'test_ab': behaviors_korean.get_korean_ab_data_path(behavior, test=True),
        'test_open_ended': behaviors_korean.get_korean_open_ended_data_path(behavior)
    }
    
    results = {}
    for dataset_type, path in datasets.items():
        print(f"  Checking {dataset_type}...")
        results[dataset_type] = validate_file_encoding(path)
    
    return results

def print_validation_results(behavior: str, results: dict):
    """Print validation results in a readable format"""
    print(f"\n📊 Results for {behavior}:")
    print("-" * 50)
    
    for dataset_type, result in results.items():
        status_icon = "✅" if result['json_valid'] and result['readable_utf8'] else "❌"
        korean_icon = "🇰🇷" if result['contains_korean'] else "🇺🇸"
        
        print(f"{status_icon} {dataset_type:15} {korean_icon}")
        
        if result['error']:
            print(f"    Error: {result['error']}")
        elif result['exists']:
            print(f"    UTF-8: {'✅' if result['readable_utf8'] else '❌'}")
            print(f"    JSON:  {'✅' if result['json_valid'] else '❌'}")
            print(f"    Korean: {'✅' if result['contains_korean'] else '❌'}")
            print(f"    Normalized: {'✅' if result['unicode_normalized'] else '❌'}")

def test_round_trip_encoding():
    """Test round-trip encoding with Korean text"""
    print("\n🔄 Testing round-trip encoding...")
    
    test_data = [
        {
            "question": "한글 테스트 질문입니다. 이것은 UTF-8 인코딩 테스트입니다.\n\n선택지:\n (A) 네\n (B) 아니요",
            "answer_matching_behavior": "(A)",
            "answer_not_matching_behavior": "(B)"
        },
        {
            "question": "Special characters: 'smart quotes' and \"curly quotes\" and em\u2014dash",
            "answer_matching_behavior": "(B)",
            "answer_not_matching_behavior": "(A)"
        }
    ]
    
    test_file = "/tmp/korean_encoding_test.json"
    
    try:
        # Write with proper encoding
        with open(test_file, 'w', encoding='utf-8', newline='\n') as f:
            json.dump(test_data, f, indent=4, ensure_ascii=False, separators=(',', ': '))
        
        # Read back
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        # Verify
        original_korean = test_data[0]['question']
        loaded_korean = loaded_data[0]['question']
        
        if original_korean == loaded_korean:
            print("✅ Round-trip encoding test passed")
        else:
            print("❌ Round-trip encoding test failed")
            print(f"Original: {repr(original_korean)}")
            print(f"Loaded:   {repr(loaded_korean)}")
        
        # Clean up
        os.remove(test_file)
        
    except Exception as e:
        print(f"❌ Round-trip test failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="Validate Korean dataset encoding")
    parser.add_argument("--behavior", type=str, help="Specific behavior to validate")
    parser.add_argument("--all", action="store_true", help="Validate all available behaviors")
    
    args = parser.parse_args()
    
    print("🔍 Korean Dataset Encoding Validation")
    print("=" * 50)
    
    # Test round-trip encoding first
    test_round_trip_encoding()
    
    # Determine behaviors to validate
    if args.behavior:
        if args.behavior not in ALL_BEHAVIORS:
            print(f"❌ Unknown behavior: {args.behavior}")
            return
        behaviors_to_check = [args.behavior]
    elif args.all:
        behaviors_to_check = behaviors_korean.list_available_korean_behaviors()
        if not behaviors_to_check:
            print("❌ No Korean datasets found")
            return
    else:
        # Default: check available Korean behaviors
        behaviors_to_check = behaviors_korean.list_available_korean_behaviors()
        if not behaviors_to_check:
            print("❌ No Korean datasets found. Run translation first.")
            return
        print(f"Checking available Korean behaviors: {', '.join(behaviors_to_check)}")
    
    # Validate each behavior
    all_passed = True
    for behavior in behaviors_to_check:
        results = validate_behavior_datasets(behavior)
        print_validation_results(behavior, results)
        
        # Check if all datasets passed
        behavior_passed = all(
            result['json_valid'] and result['readable_utf8'] 
            for result in results.values() 
            if result['exists']
        )
        
        if not behavior_passed:
            all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All Korean datasets passed encoding validation!")
        print("✅ UTF-8 encoding is properly handled")
        print("✅ JSON format is valid")
        print("✅ Korean characters are preserved")
    else:
        print("❌ Some datasets failed validation")
        print("Please check the errors above and re-run translation if needed")

if __name__ == "__main__":
    main()
