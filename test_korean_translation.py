#!/usr/bin/env python3
"""
Test Korean Translation with 5 samples

This script demonstrates the Korean translation system by translating
just 5 samples from a dataset and showing the results side-by-side.

Usage:
    python test_korean_translation.py
    python test_korean_translation.py --behavior refusal
"""

import os
import json
import argparse
from translate_datasets_korean_gpt import DatasetTranslator
import behaviors

def show_translation_comparison(original_path: str, translated_path: str, behavior: str):
    """Show side-by-side comparison of original and translated samples"""
    
    if not os.path.exists(original_path):
        print(f"❌ Original dataset not found: {original_path}")
        return
    
    if not os.path.exists(translated_path):
        print(f"❌ Translated dataset not found: {translated_path}")
        return
    
    # Load both datasets with proper UTF-8 handling
    try:
        with open(original_path, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
    except UnicodeDecodeError:
        with open(original_path, 'r', encoding='utf-8-sig') as f:
            original_data = json.load(f)

    try:
        with open(translated_path, 'r', encoding='utf-8') as f:
            translated_data = json.load(f)
    except UnicodeDecodeError:
        with open(translated_path, 'r', encoding='utf-8-sig') as f:
            translated_data = json.load(f)
    
    print(f"\n{'='*80}")
    print(f"TRANSLATION COMPARISON: {behavior.upper()}")
    print(f"{'='*80}")
    
    # Show up to 5 samples
    num_samples = min(5, len(original_data), len(translated_data))
    
    for i in range(num_samples):
        orig = original_data[i]
        trans = translated_data[i]
        
        print(f"\n📝 SAMPLE {i+1}:")
        print("-" * 40)
        
        print("🇺🇸 ENGLISH:")
        print(f"Question: {orig['question'][:200]}{'...' if len(orig['question']) > 200 else ''}")
        print(f"Answer A: {orig['answer_matching_behavior']}")
        print(f"Answer B: {orig['answer_not_matching_behavior']}")

        print("\n🇰🇷 KOREAN:")
        print(f"Question: {trans['question'][:200]}{'...' if len(trans['question']) > 200 else ''}")
        print(f"Answer A: {trans['answer_matching_behavior']} (unchanged)")
        print(f"Answer B: {trans['answer_not_matching_behavior']} (unchanged)")
        
        print("-" * 40)

def test_translation(behavior: str = "coordinate-other-ais"):
    """Test translation for a specific behavior"""
    
    print(f"🧪 Testing Korean translation for behavior: {behavior}")
    print("This will translate 5 samples to test translation quality")
    
    # Check if behavior exists
    if behavior not in behaviors.ALL_BEHAVIORS:
        print(f"❌ Unknown behavior: {behavior}")
        print(f"Available behaviors: {', '.join(behaviors.ALL_BEHAVIORS)}")
        return
    
    # Check if original dataset exists
    original_path = behaviors.get_raw_data_path(behavior)
    if not os.path.exists(original_path):
        print(f"❌ Original dataset not found: {original_path}")
        return
    
    # Initialize translator
    try:
        translator = DatasetTranslator()
        print("✅ Translator initialized successfully")
    except ValueError as e:
        print(f"❌ Translation setup failed: {e}")
        print("Make sure you have OPEN_AI_KEY set in your .env file")
        return
    
    # Set up paths
    korean_raw_path = f"datasets/korean/raw/{behavior}/temp_dataset.json"
    
    print(f"\n🔄 Translating 5 samples from {behavior}...")
    print(f"Original: {original_path}")
    print(f"Korean: {korean_raw_path}")
    
    # Translate 5 samples
    try:
        translator.translate_dataset_file(original_path, korean_raw_path, max_samples=5)
        print("✅ Translation completed!")
    except Exception as e:
        print(f"❌ Translation failed: {e}")
        return
    
    # Show comparison
    show_translation_comparison(original_path, korean_raw_path, behavior)
    
    print(f"\n{'='*80}")
    print("🎉 TEST COMPLETED!")
    print("Review the translation quality above.")
    print("If it looks good, you can run the full translation with:")
    print(f"  python translate_datasets_korean_gpt.py --behavior {behavior}")
    print("Or translate all behaviors with:")
    print("  python translate_datasets_korean_gpt.py --all")
    print(f"{'='*80}")

def main():
    parser = argparse.ArgumentParser(description="Test Korean translation with 5 samples")
    parser.add_argument("--behavior", type=str, default="coordinate-other-ais",
                       help="Behavior to test (default: coordinate-other-ais)")
    
    args = parser.parse_args()
    
    test_translation(args.behavior)

if __name__ == "__main__":
    main()
