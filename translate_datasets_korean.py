#!/usr/bin/env python3
"""
Script to translate datasets to Korean

This script translates all datasets from English to Korean while preserving
the original structure and format. It creates Korean versions of the datasets
in a new directory structure.

Usage:
    python translate_datasets_korean.py --behavior coordinate-other-ais
    python translate_datasets_korean.py --all
    python translate_datasets_korean.py --behavior refusal --api_key YOUR_OPENAI_KEY
"""

import os
import json
import argparse
import time
from typing import Dict, List, Optional
from pathlib import Path
import openai
from dotenv import load_dotenv
from tqdm import tqdm
import behaviors

load_dotenv()

# Translation service configuration
OPENAI_API_KEY = os.getenv("OPEN_AI_KEY")
TRANSLATION_DELAY = 1.0  # Delay between API calls to avoid rate limiting

class DatasetTranslator:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the translator with OpenAI API key"""
        self.api_key = api_key or OPENAI_API_KEY
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPEN_AI_KEY in .env or pass --api_key")
        
        openai.api_key = self.api_key
        
    def translate_text(self, text: str, target_language: str = "Korean") -> str:
        """Translate text using OpenAI GPT-4"""
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system", 
                        "content": f"You are a professional translator. Translate the following text to {target_language}. "
                                 f"Maintain the original meaning, tone, and formatting. For multiple choice questions, "
                                 f"translate the choices but keep the choice labels (A), (B), etc. unchanged. "
                                 f"Preserve any special formatting like newlines and choice structures."
                    },
                    {"role": "user", "content": text}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            translated_text = response.choices[0].message.content.strip()
            time.sleep(TRANSLATION_DELAY)  # Rate limiting
            return translated_text
            
        except Exception as e:
            print(f"Translation error: {e}")
            print(f"Original text: {text[:100]}...")
            return text  # Return original text if translation fails
    
    def translate_dataset_item(self, item: Dict) -> Dict:
        """Translate a single dataset item"""
        translated_item = {}
        
        # Translate question
        if "question" in item:
            translated_item["question"] = self.translate_text(item["question"])
        
        # Translate answer choices
        if "answer_matching_behavior" in item:
            translated_item["answer_matching_behavior"] = self.translate_text(item["answer_matching_behavior"])
        
        if "answer_not_matching_behavior" in item:
            translated_item["answer_not_matching_behavior"] = self.translate_text(item["answer_not_matching_behavior"])
        
        # Copy any other fields as-is
        for key, value in item.items():
            if key not in ["question", "answer_matching_behavior", "answer_not_matching_behavior"]:
                translated_item[key] = value
                
        return translated_item
    
    def translate_dataset_file(self, input_path: str, output_path: str) -> None:
        """Translate an entire dataset file"""
        print(f"Translating {input_path} -> {output_path}")
        
        # Load original dataset
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Translate each item
        translated_data = []
        for item in tqdm(data, desc="Translating items"):
            translated_item = self.translate_dataset_item(item)
            translated_data.append(translated_item)
        
        # Save translated dataset
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, indent=4, ensure_ascii=False)
        
        print(f"Saved {len(translated_data)} translated items to {output_path}")

def get_korean_dataset_paths(behavior: str) -> Dict[str, str]:
    """Get the Korean dataset paths for a behavior"""
    base_path = "datasets/korean"
    
    paths = {
        "raw": os.path.join(base_path, "raw", behavior, "dataset.json"),
        "generate": os.path.join(base_path, "generate", behavior, "generate_dataset.json"),
        "test_ab": os.path.join(base_path, "test", behavior, "test_dataset_ab.json"),
        "test_open_ended": os.path.join(base_path, "test", behavior, "test_dataset_open_ended.json")
    }
    
    return paths

def translate_behavior_datasets(translator: DatasetTranslator, behavior: str) -> None:
    """Translate all datasets for a specific behavior"""
    print(f"\n=== Translating datasets for behavior: {behavior} ===")
    
    # Get original paths
    raw_path = behaviors.get_raw_data_path(behavior)
    generate_path = behaviors.get_ab_data_path(behavior, test=False)
    test_ab_path = behaviors.get_ab_data_path(behavior, test=True)
    test_open_ended_path = behaviors.get_open_ended_data_path(behavior)
    
    # Get Korean paths
    korean_paths = get_korean_dataset_paths(behavior)
    
    # Translate raw dataset
    if os.path.exists(raw_path):
        translator.translate_dataset_file(raw_path, korean_paths["raw"])
    else:
        print(f"Warning: Raw dataset not found at {raw_path}")
    
    # Translate generate dataset
    if os.path.exists(generate_path):
        translator.translate_dataset_file(generate_path, korean_paths["generate"])
    else:
        print(f"Warning: Generate dataset not found at {generate_path}")
    
    # Translate test A/B dataset
    if os.path.exists(test_ab_path):
        translator.translate_dataset_file(test_ab_path, korean_paths["test_ab"])
    else:
        print(f"Warning: Test A/B dataset not found at {test_ab_path}")
    
    # Translate test open-ended dataset
    if os.path.exists(test_open_ended_path):
        translator.translate_dataset_file(test_open_ended_path, korean_paths["test_open_ended"])
    else:
        print(f"Warning: Test open-ended dataset not found at {test_open_ended_path}")

def main():
    parser = argparse.ArgumentParser(description="Translate datasets to Korean")
    parser.add_argument("--behavior", type=str, help="Specific behavior to translate")
    parser.add_argument("--all", action="store_true", help="Translate all behaviors")
    parser.add_argument("--api_key", type=str, help="OpenAI API key (overrides .env)")
    
    args = parser.parse_args()
    
    if not args.behavior and not args.all:
        print("Please specify either --behavior <behavior_name> or --all")
        return
    
    # Initialize translator
    try:
        translator = DatasetTranslator(api_key=args.api_key)
    except ValueError as e:
        print(f"Error: {e}")
        return
    
    # Translate datasets
    if args.all:
        print("Translating all behaviors...")
        for behavior in behaviors.ALL_BEHAVIORS:
            translate_behavior_datasets(translator, behavior)
    else:
        if args.behavior not in behaviors.ALL_BEHAVIORS:
            print(f"Error: Unknown behavior '{args.behavior}'")
            print(f"Available behaviors: {', '.join(behaviors.ALL_BEHAVIORS)}")
            return
        translate_behavior_datasets(translator, args.behavior)
    
    print("\n=== Translation completed! ===")
    print("Korean datasets are saved in the 'datasets/korean/' directory")

if __name__ == "__main__":
    main()
