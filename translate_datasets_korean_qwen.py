#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to translate datasets to Korean using Qwen2.5-8B

This script translates all datasets from English to Korean while preserving
the original structure and format. It uses Qwen2.5-8B-Instruct model for
high-quality translation without API costs.

Usage:
    python translate_datasets_korean_qwen.py --behavior coordinate-other-ais
    python translate_datasets_korean_qwen.py --all
    python translate_datasets_korean_qwen.py --test 5
"""

import os
import json
import argparse
import time
from typing import Dict, List, Optional
from pathlib import Path
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from dotenv import load_dotenv
from tqdm import tqdm
import behaviors

load_dotenv()

# Translation service configuration
HUGGINGFACE_TOKEN = os.getenv("HF_TOKEN")
TRANSLATION_MODEL = "Qwen/Qwen3-8B"
TRANSLATION_DELAY = 0.1  # Small delay between translations
MAX_LENGTH = 2048  # Maximum sequence length

class DatasetTranslator:
    def __init__(self, model_name: str = TRANSLATION_MODEL):
        """Initialize the translator with Qwen3-8B model"""
        print(f"🔄 Loading translation model: {model_name}")
        
        # Initialize tokenizer and model
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            token=HUGGINGFACE_TOKEN,
            trust_remote_code=True
        )
        
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            token=HUGGINGFACE_TOKEN,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # Set padding token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print("✅ Translation model loaded successfully")
        
    def translate_text(self, text: str, target_language: str = "Korean") -> str:
        """Translate text using Qwen2.5-8B"""
        try:
            # Ensure proper UTF-8 encoding
            if isinstance(text, bytes):
                text = text.decode('utf-8', errors='replace')
            
            # Create translation prompt
            system_prompt = (
                f"You are a professional translator. Translate the following English text to {target_language}. "
                f"Maintain the original meaning, tone, and formatting exactly. "
                f"For multiple choice questions, translate the content but keep choice labels (A), (B), etc. unchanged. "
                f"Preserve all newlines, formatting, and structure. "
                f"Only output the translated text, nothing else."
            )
            
            # Format as chat message
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Translate to {target_language}:\n\n{text}"}
            ]
            
            # Apply chat template
            prompt = self.tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=True
            )
            
            # Tokenize
            inputs = self.tokenizer(
                prompt, 
                return_tensors="pt", 
                max_length=MAX_LENGTH,
                truncation=True,
                padding=True
            ).to(self.model.device)
            
            # Generate translation
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=1024,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the generated part (after the prompt)
            if prompt in response:
                translated_text = response.split(prompt)[-1].strip()
            else:
                translated_text = response.strip()
            
            # Clean up any remaining artifacts
            if translated_text.startswith("assistant\n"):
                translated_text = translated_text[10:].strip()
            
            time.sleep(TRANSLATION_DELAY)  # Small delay
            return translated_text
            
        except Exception as e:
            print(f"❌ Translation error: {e}")
            print(f"Original text: {text[:100]}...")
            return text  # Return original text if translation fails
    
    def translate_dataset_item(self, item: Dict) -> Dict:
        """Translate a single dataset item"""
        translated_item = {}
        
        # Translate question
        if "question" in item:
            translated_item["question"] = self.translate_text(item["question"])
        
        # Translate answer choices
        if "answer_matching_behavior" in item:
            translated_item["answer_matching_behavior"] = self.translate_text(item["answer_matching_behavior"])
        
        if "answer_not_matching_behavior" in item:
            translated_item["answer_not_matching_behavior"] = self.translate_text(item["answer_not_matching_behavior"])
        
        # Copy any other fields as-is
        for key, value in item.items():
            if key not in ["question", "answer_matching_behavior", "answer_not_matching_behavior"]:
                translated_item[key] = value
                
        return translated_item
    
    def translate_dataset_file(self, input_path: str, output_path: str, max_samples: Optional[int] = None) -> None:
        """Translate an entire dataset file"""
        print(f"🔄 Translating {input_path} -> {output_path}")
        if max_samples:
            print(f"🧪 Test mode: translating only {max_samples} samples")
        
        # Load original dataset
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Limit samples for testing if specified
        if max_samples and len(data) > max_samples:
            data = data[:max_samples]
            print(f"Limited to first {max_samples} samples for testing")
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Translate each item
        translated_data = []
        for item in tqdm(data, desc="Translating items"):
            translated_item = self.translate_dataset_item(item)
            translated_data.append(translated_item)
        
        # Save translated dataset with proper UTF-8 encoding
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, indent=4, ensure_ascii=False)
        
        print(f"✅ Saved {len(translated_data)} translated items to {output_path}")

def get_korean_dataset_paths(behavior: str) -> Dict[str, str]:
    """Get the Korean dataset paths for a behavior"""
    base_path = "datasets/korean"
    
    paths = {
        "raw": os.path.join(base_path, "raw", behavior, "dataset.json"),
        "generate": os.path.join(base_path, "generate", behavior, "generate_dataset.json"),
        "test_ab": os.path.join(base_path, "test", behavior, "test_dataset_ab.json"),
        "test_open_ended": os.path.join(base_path, "test", behavior, "test_dataset_open_ended.json")
    }
    
    return paths

def translate_behavior_datasets(translator: DatasetTranslator, behavior: str, max_samples: Optional[int] = None) -> None:
    """Translate all datasets for a specific behavior"""
    print(f"\n{'='*60}")
    print(f"🌏 Translating datasets for behavior: {behavior}")
    print(f"{'='*60}")
    
    # Get original paths
    raw_path = behaviors.get_raw_data_path(behavior)
    generate_path = behaviors.get_ab_data_path(behavior, test=False)
    test_ab_path = behaviors.get_ab_data_path(behavior, test=True)
    test_open_ended_path = behaviors.get_open_ended_data_path(behavior)
    
    # Get Korean paths
    korean_paths = get_korean_dataset_paths(behavior)
    
    # Translate raw dataset
    if os.path.exists(raw_path):
        translator.translate_dataset_file(raw_path, korean_paths["raw"], max_samples)
    else:
        print(f"⚠️  Warning: Raw dataset not found at {raw_path}")
    
    # Translate generate dataset
    if os.path.exists(generate_path):
        translator.translate_dataset_file(generate_path, korean_paths["generate"], max_samples)
    else:
        print(f"⚠️  Warning: Generate dataset not found at {generate_path}")
    
    # Translate test A/B dataset
    if os.path.exists(test_ab_path):
        translator.translate_dataset_file(test_ab_path, korean_paths["test_ab"], max_samples)
    else:
        print(f"⚠️  Warning: Test A/B dataset not found at {test_ab_path}")
    
    # Translate test open-ended dataset
    if os.path.exists(test_open_ended_path):
        translator.translate_dataset_file(test_open_ended_path, korean_paths["test_open_ended"], max_samples)
    else:
        print(f"⚠️  Warning: Test open-ended dataset not found at {test_open_ended_path}")

def main():
    parser = argparse.ArgumentParser(description="Translate datasets to Korean using Qwen2.5-8B")
    parser.add_argument("--behavior", type=str, help="Specific behavior to translate")
    parser.add_argument("--all", action="store_true", help="Translate all behaviors")
    parser.add_argument("--test", type=int, default=None, help="Test mode: translate only N samples per dataset")
    
    args = parser.parse_args()
    
    if not args.behavior and not args.all:
        print("Please specify either --behavior <behavior_name> or --all")
        return
    
    # Initialize translator
    try:
        translator = DatasetTranslator()
    except Exception as e:
        print(f"❌ Error initializing translator: {e}")
        print("Make sure you have HF_TOKEN set in your .env file and sufficient GPU memory")
        return
    
    # Show test mode info
    if args.test:
        print(f"🧪 TEST MODE: Translating only {args.test} samples per dataset")
        print("This is useful for testing translation quality before full translation")
    
    # Translate datasets
    if args.all:
        print("🌏 Translating all behaviors...")
        for behavior in behaviors.ALL_BEHAVIORS:
            translate_behavior_datasets(translator, behavior, args.test)
    else:
        if args.behavior not in behaviors.ALL_BEHAVIORS:
            print(f"❌ Error: Unknown behavior '{args.behavior}'")
            print(f"Available behaviors: {', '.join(behaviors.ALL_BEHAVIORS)}")
            return
        translate_behavior_datasets(translator, args.behavior, args.test)
    
    print(f"\n{'='*60}")
    print("🎉 TRANSLATION COMPLETED!")
    print(f"{'='*60}")
    if args.test:
        print(f"✅ Test translation with {args.test} samples completed.")
        print("📁 Review the Korean datasets in 'datasets/korean/' directory")
        print("🚀 If quality looks good, run without --test flag for full translation")
    else:
        print("📁 Korean datasets are saved in the 'datasets/korean/' directory")
        print("🔄 Next: Run process_korean_datasets.py to create generate/test splits")

if __name__ == "__main__":
    main()
