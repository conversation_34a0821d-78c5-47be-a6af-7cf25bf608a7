#!/usr/bin/env python3
"""
Validate and clean Korean translation results

This script helps validate the quality of Korean translations and clean up
any reasoning artifacts that might have been generated by the model.

Usage:
    python validate_korean_translation.py --file datasets/korean/raw/coordinate-other-ais/temp_dataset.json
    python validate_korean_translation.py --behavior coordinate-other-ais
"""

import os
import json
import argparse
import re
from typing import Dict, List
import behaviors_korean

def has_korean_text(text: str) -> bool:
    """Check if text contains Korean characters"""
    return any('\uac00' <= char <= '\ud7af' for char in text)

def clean_reasoning_artifacts(text: str) -> str:
    """Remove reasoning artifacts from translated text"""
    # Remove common reasoning patterns
    reasoning_patterns = [
        r"Let me translate.*?:",
        r"I need to.*?:",
        r"The translation is.*?:",
        r"Here's the translation.*?:",
        r"Translation.*?:",
        r"Korean translation.*?:",
        r"Step by step.*?:",
        r"First.*?:",
        r"번역하면.*?:",
        r"한국어로는.*?:",
        r"다음과 같이.*?:",
    ]
    
    cleaned = text
    for pattern in reasoning_patterns:
        cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE | re.DOTALL)
    
    # Remove ## markers if present
    cleaned = re.sub(r'##', '', cleaned)
    
    # Clean up extra whitespace
    cleaned = ' '.join(cleaned.split())
    
    return cleaned.strip()

def validate_translation_item(item: Dict, index: int) -> Dict:
    """Validate and clean a single translation item"""
    issues = []
    cleaned_item = {}
    
    # Required fields
    required_fields = ["question", "answer_matching_behavior", "answer_not_matching_behavior"]
    
    for field in required_fields:
        if field not in item:
            issues.append(f"Missing field: {field}")
            cleaned_item[field] = ""
        else:
            original_text = item[field]
            cleaned_text = clean_reasoning_artifacts(original_text)
            
            # Check if translation looks reasonable
            if not has_korean_text(cleaned_text) and len(cleaned_text) > 10:
                issues.append(f"Field '{field}' may not be properly translated")
            
            # Check for excessive length (might indicate reasoning artifacts)
            if len(cleaned_text) > len(original_text) * 2:
                issues.append(f"Field '{field}' unusually long, may contain reasoning")
            
            cleaned_item[field] = cleaned_text
    
    # Copy other fields as-is
    for key, value in item.items():
        if key not in required_fields:
            cleaned_item[key] = value
    
    return {
        "item": cleaned_item,
        "issues": issues,
        "index": index
    }

def validate_dataset_file(file_path: str) -> Dict:
    """Validate an entire dataset file"""
    print(f"🔍 Validating: {file_path}")
    
    if not os.path.exists(file_path):
        return {"error": f"File not found: {file_path}"}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        return {"error": f"Failed to load JSON: {e}"}
    
    results = {
        "total_items": len(data),
        "valid_items": 0,
        "items_with_issues": 0,
        "cleaned_data": [],
        "issues_summary": [],
        "korean_coverage": 0
    }
    
    all_issues = []
    korean_count = 0
    
    for i, item in enumerate(data):
        validation_result = validate_translation_item(item, i)
        
        results["cleaned_data"].append(validation_result["item"])
        
        if validation_result["issues"]:
            results["items_with_issues"] += 1
            all_issues.extend([f"Item {i+1}: {issue}" for issue in validation_result["issues"]])
        else:
            results["valid_items"] += 1
        
        # Check Korean coverage
        if has_korean_text(validation_result["item"].get("question", "")):
            korean_count += 1
    
    results["korean_coverage"] = korean_count / len(data) * 100 if data else 0
    results["issues_summary"] = all_issues
    
    return results

def show_validation_report(results: Dict, file_path: str):
    """Show validation report"""
    print(f"\n📊 VALIDATION REPORT: {file_path}")
    print("=" * 60)
    
    if "error" in results:
        print(f"❌ Error: {results['error']}")
        return
    
    print(f"Total items: {results['total_items']}")
    print(f"Valid items: {results['valid_items']}")
    print(f"Items with issues: {results['items_with_issues']}")
    print(f"Korean coverage: {results['korean_coverage']:.1f}%")
    
    if results['items_with_issues'] > 0:
        print(f"\n⚠️  Issues found:")
        for issue in results['issues_summary'][:10]:  # Show first 10 issues
            print(f"  - {issue}")
        
        if len(results['issues_summary']) > 10:
            print(f"  ... and {len(results['issues_summary']) - 10} more issues")
    
    # Show sample
    if results['cleaned_data']:
        sample = results['cleaned_data'][0]
        print(f"\n📝 Sample item:")
        print(f"Question: {sample['question'][:100]}{'...' if len(sample['question']) > 100 else ''}")
        print(f"Answer A: {sample['answer_matching_behavior']}")
        print(f"Answer B: {sample['answer_not_matching_behavior']}")
    
    print("=" * 60)

def save_cleaned_dataset(results: Dict, output_path: str):
    """Save cleaned dataset"""
    if "error" in results:
        print(f"❌ Cannot save due to error: {results['error']}")
        return
    
    # Create output directory
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save cleaned data
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results['cleaned_data'], f, indent=4, ensure_ascii=False)
    
    print(f"✅ Saved cleaned dataset to: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Validate Korean translation results")
    parser.add_argument("--file", type=str, help="Specific file to validate")
    parser.add_argument("--behavior", type=str, help="Validate all files for a behavior")
    parser.add_argument("--clean", action="store_true", help="Save cleaned version")
    
    args = parser.parse_args()
    
    if not args.file and not args.behavior:
        print("Please specify either --file or --behavior")
        return
    
    files_to_validate = []
    
    if args.file:
        files_to_validate.append(args.file)
    
    if args.behavior:
        # Validate all Korean files for the behavior
        base_path = f"datasets/korean/raw/{args.behavior}"
        for filename in ["dataset.json", "temp_dataset.json"]:
            file_path = os.path.join(base_path, filename)
            if os.path.exists(file_path):
                files_to_validate.append(file_path)
    
    if not files_to_validate:
        print("❌ No files found to validate")
        return
    
    for file_path in files_to_validate:
        results = validate_dataset_file(file_path)
        show_validation_report(results, file_path)
        
        if args.clean and "error" not in results:
            # Save cleaned version
            if file_path.endswith("temp_dataset.json"):
                output_path = file_path.replace("temp_dataset.json", "dataset.json")
            else:
                output_path = file_path.replace(".json", "_cleaned.json")
            
            save_cleaned_dataset(results, output_path)

if __name__ == "__main__":
    main()
